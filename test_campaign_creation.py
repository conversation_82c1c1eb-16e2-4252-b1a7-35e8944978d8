#!/usr/bin/env python3
"""
Test script to verify campaign creation and workflow state creation
"""

import os
import sys
import django
from datetime import date, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coffee_meetings.settings')
django.setup()

from campaigns.models import Campaign, CampaignWorkflowState
from users.models import HRManager

def test_campaign_creation():
    """Test that creating a campaign automatically creates workflow state"""
    
    # Get or create a test HR manager
    hr_manager, created = HRManager.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'name': 'Test Manager',
            'company': 'Test Company'
        }
    )
    
    print(f"HR Manager: {hr_manager.name} ({'created' if created else 'existing'})")
    
    # Create a test campaign
    campaign = Campaign.objects.create(
        title='Test Campaign - Workflow State',
        description='Testing automatic workflow state creation',
        start_date=date.today() + timedelta(days=1),
        end_date=date.today() + timedelta(days=30),
        hr_manager=hr_manager
    )
    
    print(f"Campaign created: {campaign.title} (ID: {campaign.id})")
    
    # Check if workflow state was created automatically
    try:
        workflow_state = campaign.workflow_state
        print(f"✅ Workflow state found:")
        print(f"   Current step: {workflow_state.current_step}")
        print(f"   Completed steps: {workflow_state.completed_steps}")
        print(f"   Step data keys: {list(workflow_state.step_data.keys())}")
        
        # Verify the workflow state is correct
        assert workflow_state.current_step == 2, f"Expected current_step=2, got {workflow_state.current_step}"
        assert 1 in workflow_state.completed_steps, f"Expected step 1 to be completed, got {workflow_state.completed_steps}"
        assert '1' in workflow_state.step_data, f"Expected step 1 data, got keys: {list(workflow_state.step_data.keys())}"
        
        print("✅ All workflow state assertions passed!")
        
    except CampaignWorkflowState.DoesNotExist:
        print("❌ Workflow state was NOT created automatically!")
        return False
    
    # Clean up
    campaign.delete()
    if created:
        hr_manager.delete()
    
    print("✅ Test completed successfully!")
    return True

if __name__ == '__main__':
    success = test_campaign_creation()
    sys.exit(0 if success else 1)
