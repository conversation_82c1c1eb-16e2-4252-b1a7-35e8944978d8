# Generated manually to fix missing workflow states

from django.db import migrations


def create_missing_workflow_states(apps, schema_editor):
    """Create workflow states for campaigns that don't have them"""
    Campaign = apps.get_model('campaigns', 'Campaign')
    CampaignWorkflowState = apps.get_model('campaigns', 'CampaignWorkflowState')
    
    campaigns_without_workflow = Campaign.objects.filter(workflow_state__isnull=True)
    
    for campaign in campaigns_without_workflow:
        CampaignWorkflowState.objects.create(
            campaign=campaign,
            current_step=2,  # Start from step 2 (Upload Employees)
            completed_steps=[1],  # Step 1 (Create Campaign) is already completed
            step_data={
                '1': {
                    'title': campaign.title,
                    'description': campaign.description,
                    'start_date': campaign.start_date.isoformat(),
                    'end_date': campaign.end_date.isoformat(),
                    'created_at': campaign.created_at.isoformat()
                }
            }
        )


def reverse_create_missing_workflow_states(apps, schema_editor):
    """Reverse operation - remove workflow states created by this migration"""
    # We don't want to remove workflow states as it could break data integrity
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0006_alter_campaign_options_and_more'),
    ]

    operations = [
        migrations.RunPython(
            create_missing_workflow_states,
            reverse_create_missing_workflow_states,
        ),
    ]
