from django.core.management.base import BaseCommand
from django.apps import apps


class Command(BaseCommand):
    help = 'Simple test to check if signals are working'

    def handle(self, *args, **options):
        self.stdout.write('Testing signal registration...')
        
        # Check if campaigns app is loaded
        try:
            campaigns_app = apps.get_app_config('campaigns')
            self.stdout.write(f"✅ Campaigns app loaded: {campaigns_app.name}")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error loading campaigns app: {e}"))
            return
        
        # Try to import the models
        try:
            from campaigns.models import Campaign, CampaignWorkflowState
            self.stdout.write("✅ Campaign models imported successfully")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error importing models: {e}"))
            return
        
        # Check if we can access the signal
        try:
            from django.db.models.signals import post_save
            from django.dispatch import receiver
            
            # Get all receivers for Campaign post_save signal
            receivers = post_save._live_receivers(sender=Campaign)
            self.stdout.write(f"✅ Found {len(receivers)} post_save receivers for Campaign model")
            
            for receiver in receivers:
                self.stdout.write(f"   - {receiver}")
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error checking signals: {e}"))
        
        self.stdout.write(self.style.SUCCESS('✅ Signal test completed!'))
