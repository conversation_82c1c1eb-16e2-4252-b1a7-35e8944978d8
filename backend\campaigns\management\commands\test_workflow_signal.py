from django.core.management.base import BaseCommand
from campaigns.models import Campaign, CampaignWorkflowState
from users.models import HRManager
from datetime import date, timedelta


class Command(BaseCommand):
    help = 'Test that campaign creation automatically creates workflow state'

    def handle(self, *args, **options):
        self.stdout.write('Testing campaign workflow state signal...')
        
        # Get or create a test HR manager
        hr_manager, created = HRManager.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'name': 'Test Signal Manager',
                'company': 'Test Company'
            }
        )
        
        self.stdout.write(f"HR Manager: {hr_manager.name} ({'created' if created else 'existing'})")
        
        # Create a test campaign
        campaign = Campaign.objects.create(
            title='Test Campaign - Signal Test',
            description='Testing automatic workflow state creation',
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30),
            hr_manager=hr_manager
        )
        
        self.stdout.write(f"Campaign created: {campaign.title} (ID: {campaign.id})")
        
        # Check if workflow state was created automatically
        try:
            workflow_state = campaign.workflow_state
            self.stdout.write(self.style.SUCCESS('✅ Workflow state found:'))
            self.stdout.write(f"   Current step: {workflow_state.current_step}")
            self.stdout.write(f"   Completed steps: {workflow_state.completed_steps}")
            self.stdout.write(f"   Step data keys: {list(workflow_state.step_data.keys())}")
            
            # Verify the workflow state is correct
            if workflow_state.current_step == 2:
                self.stdout.write(self.style.SUCCESS('✅ Current step is correct (2)'))
            else:
                self.stdout.write(self.style.ERROR(f'❌ Current step is wrong: expected 2, got {workflow_state.current_step}'))
            
            if 1 in workflow_state.completed_steps:
                self.stdout.write(self.style.SUCCESS('✅ Step 1 is marked as completed'))
            else:
                self.stdout.write(self.style.ERROR(f'❌ Step 1 is not completed: {workflow_state.completed_steps}'))
            
            if '1' in workflow_state.step_data:
                self.stdout.write(self.style.SUCCESS('✅ Step 1 data is present'))
            else:
                self.stdout.write(self.style.ERROR(f'❌ Step 1 data is missing: {list(workflow_state.step_data.keys())}'))
            
        except CampaignWorkflowState.DoesNotExist:
            self.stdout.write(self.style.ERROR('❌ Workflow state was NOT created automatically!'))
            
        # Clean up
        campaign.delete()
        if created:
            hr_manager.delete()
        
        self.stdout.write(self.style.SUCCESS('✅ Test completed!'))
